﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug(DLL)|Win32">
      <Configuration>Debug(DLL)</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug(DLL)|x64">
      <Configuration>Debug(DLL)</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug(XP)|Win32">
      <Configuration>Debug(XP)</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug(XP)|x64">
      <Configuration>Debug(XP)</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release(DLL)|Win32">
      <Configuration>Release(DLL)</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release(DLL)|x64">
      <Configuration>Release(DLL)</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release(XP)|Win32">
      <Configuration>Release(XP)</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release(XP)|x64">
      <Configuration>Release(XP)</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A2C53563-46F5-4D87-903F-3F1F2FDB2DEB}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>Xantos</RootNamespace>
    <ProjectName>BlackBone</ProjectName>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug(XP)|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v141_xp</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug(DLL)|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug(XP)|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v141_xp</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug(DLL)|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release(XP)|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v141_xp</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release(DLL)|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release(XP)|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v141_xp</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release(DLL)|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.props" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug(XP)|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug(DLL)|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug(XP)|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug(DLL)|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release(XP)|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release(DLL)|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release(XP)|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release(DLL)|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)build\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <EnableCppCoreCheck>true</EnableCppCoreCheck>
    <CodeAnalysisRuleSet>C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\Team Tools\Static Analysis Tools\Rule Sets\NativeRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <RunCodeAnalysis>false</RunCodeAnalysis>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug(XP)|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)build\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug(DLL)|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)build\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)build\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <EnableCppCoreCheck>true</EnableCppCoreCheck>
    <CodeAnalysisRuleSet>C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\Team Tools\Static Analysis Tools\Rule Sets\NativeRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <RunCodeAnalysis>false</RunCodeAnalysis>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug(XP)|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)build\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug(DLL)|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)build\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)build\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release(XP)|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)build\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release(DLL)|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)build\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)build\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release(XP)|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)build\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release(DLL)|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)build\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;CONSOLE_TRACE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <BasicRuntimeChecks>UninitializedLocalUsageCheck</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4100</DisableSpecificWarnings>
      <EnablePREfast>false</EnablePREfast>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>false</MinimalRebuild>
      <AdditionalIncludeDirectories>$(ProjectDir)..;F:\Visual Studio2022\VC\Tools\MSVC\14.44.35207\atlmfc\include;$(VCToolsInstallDir)include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>true</LinkLibraryDependencies>
    </ProjectReference>
    <Lib>
      <AdditionalLibraryDirectories>$(ProjectDir)..\3rd_party\DIA\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>diaguids.lib;Ole32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
    <PostBuildEvent>
      <Command>xcopy "$(ProjectDir)..\..\DIA\$(Platform)\msdia140.dll" "$(TargetDir)" /Y
xcopy "$(ProjectDir)..\..\DIA\$(Platform)\symsrv.dll" "$(TargetDir)" /Y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug(XP)|Win32'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;CONSOLE_TRACE;XP_BUILD;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <BasicRuntimeChecks>UninitializedLocalUsageCheck</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4100</DisableSpecificWarnings>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>false</MinimalRebuild>
      <AdditionalIncludeDirectories>$(ProjectDir)..;F:\Visual Studio2022\VC\Tools\MSVC\14.44.35207\atlmfc\include;$(VCToolsInstallDir)include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>true</LinkLibraryDependencies>
    </ProjectReference>
    <Lib>
      <AdditionalLibraryDirectories>$(ProjectDir)..\3rd_party\DIA\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>diaguids.lib;Ole32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
    <PostBuildEvent>
      <Command>xcopy "$(ProjectDir)..\..\DIA\$(Platform)\msdia140.dll" "$(TargetDir)" /Y
xcopy "$(ProjectDir)..\..\DIA\$(Platform)\symsrv.dll" "$(TargetDir)" /Y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug(DLL)|Win32'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_DLL;CONSOLE_TRACE;BLACKBONE_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <BasicRuntimeChecks>UninitializedLocalUsageCheck</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4100</DisableSpecificWarnings>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>false</MinimalRebuild>
      <AdditionalIncludeDirectories>$(ProjectDir)..;F:\Visual Studio2022\VC\Tools\MSVC\14.44.35207\atlmfc\include;$(VCToolsInstallDir)include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>diaguids.lib;Ole32.lib;$(ProjectDir)..\3rd_party\BeaEngine\Win32\Dll\BeaEngineCheetah.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <IgnoreSpecificDefaultLibraries>
      </IgnoreSpecificDefaultLibraries>
      <AdditionalLibraryDirectories>$(ProjectDir)..\3rd_party\DIA\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ModuleDefinitionFile>Exports.def</ModuleDefinitionFile>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>true</LinkLibraryDependencies>
    </ProjectReference>
    <PostBuildEvent>
      <Command>xcopy "$(ProjectDir)..\..\DIA\$(Platform)\msdia140.dll" "$(TargetDir)" /Y
xcopy "$(ProjectDir)..\..\DIA\$(Platform)\symsrv.dll" "$(TargetDir)" /Y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;CONSOLE_TRACE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <DisableSpecificWarnings>4100</DisableSpecificWarnings>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <EnablePREfast>false</EnablePREfast>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>false</MinimalRebuild>
      <AdditionalIncludeDirectories>$(ProjectDir)..;F:\Visual Studio2022\VC\Tools\MSVC\14.44.35207\atlmfc\include;$(VCToolsInstallDir)include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>true</LinkLibraryDependencies>
    </ProjectReference>
    <Lib>
      <AdditionalLibraryDirectories>$(ProjectDir)..\3rd_party\DIA\lib\amd64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>diaguids.lib;Ole32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
    <PostBuildEvent>
      <Command>xcopy "$(ProjectDir)..\..\DIA\$(Platform)\msdia140.dll" "$(TargetDir)" /Y
xcopy "$(ProjectDir)..\..\DIA\$(Platform)\symsrv.dll" "$(TargetDir)" /Y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug(XP)|x64'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;CONSOLE_TRACE;XP_BUILD;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <DisableSpecificWarnings>4100</DisableSpecificWarnings>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>false</MinimalRebuild>
      <AdditionalIncludeDirectories>$(ProjectDir)..;F:\Visual Studio2022\VC\Tools\MSVC\14.44.35207\atlmfc\include;$(VCToolsInstallDir)include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>true</LinkLibraryDependencies>
    </ProjectReference>
    <Lib>
      <AdditionalLibraryDirectories>$(ProjectDir)..\3rd_party\DIA\lib\amd64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>diaguids.lib;Ole32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
    <PostBuildEvent>
      <Command>xcopy "$(ProjectDir)..\..\DIA\$(Platform)\msdia140.dll" "$(TargetDir)" /Y
xcopy "$(ProjectDir)..\..\DIA\$(Platform)\symsrv.dll" "$(TargetDir)" /Y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug(DLL)|x64'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_DLL;CONSOLE_TRACE;BLACKBONE_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <DisableSpecificWarnings>4100</DisableSpecificWarnings>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>false</MinimalRebuild>
      <AdditionalIncludeDirectories>$(ProjectDir)..;F:\Visual Studio2022\VC\Tools\MSVC\14.44.35207\atlmfc\include;$(VCToolsInstallDir)include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>diaguids.lib;Ole32.lib;$(ProjectDir)..\3rd_party\BeaEngine\Win64\Dll\BeaEngineCheetah64.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(ProjectDir)..\3rd_party\DIA\lib\amd64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ModuleDefinitionFile>Exports.def</ModuleDefinitionFile>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>true</LinkLibraryDependencies>
    </ProjectReference>
    <PostBuildEvent>
      <Command>xcopy "$(ProjectDir)..\..\DIA\$(Platform)\msdia140.dll" "$(TargetDir)" /Y
xcopy "$(ProjectDir)..\..\DIA\$(Platform)\symsrv.dll" "$(TargetDir)" /Y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>Full</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;BLACKBONE_NO_TRACE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <DisableSpecificWarnings>4100</DisableSpecificWarnings>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <AdditionalIncludeDirectories>$(ProjectDir)..;F:\Visual Studio2022\VC\Tools\MSVC\14.44.35207\atlmfc\include;$(VCToolsInstallDir)include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>true</LinkLibraryDependencies>
    </ProjectReference>
    <Lib>
      <AdditionalLibraryDirectories>$(ProjectDir)..\3rd_party\DIA\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>diaguids.lib;Ole32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
    <PostBuildEvent>
      <Command>xcopy "$(ProjectDir)..\..\DIA\$(Platform)\msdia140.dll" "$(TargetDir)" /Y
xcopy "$(ProjectDir)..\..\DIA\$(Platform)\symsrv.dll" "$(TargetDir)" /Y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release(XP)|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>Full</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;BLACKBONE_NO_TRACE;XP_BUILD;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <DisableSpecificWarnings>4100</DisableSpecificWarnings>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <AdditionalIncludeDirectories>$(ProjectDir)..;F:\Visual Studio2022\VC\Tools\MSVC\14.44.35207\atlmfc\include;$(VCToolsInstallDir)include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>true</LinkLibraryDependencies>
    </ProjectReference>
    <Lib>
      <AdditionalLibraryDirectories>$(ProjectDir)..\3rd_party\DIA\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>diaguids.lib;Ole32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
    <PostBuildEvent>
      <Command>xcopy "$(ProjectDir)..\..\DIA\$(Platform)\msdia140.dll" "$(TargetDir)" /Y
xcopy "$(ProjectDir)..\..\DIA\$(Platform)\symsrv.dll" "$(TargetDir)" /Y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release(DLL)|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>Full</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_DLL;BLACKBONE_NO_TRACE;BLACKBONE_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>
      </SDLCheck>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <DisableSpecificWarnings>4100</DisableSpecificWarnings>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <AdditionalIncludeDirectories>$(ProjectDir)..;F:\Visual Studio2022\VC\Tools\MSVC\14.44.35207\atlmfc\include;$(VCToolsInstallDir)include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>diaguids.lib;Ole32.lib;$(ProjectDir)..\3rd_party\BeaEngine\Win32\Dll\BeaEngineCheetah.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <ShowProgress>NotSet</ShowProgress>
      <IgnoreSpecificDefaultLibraries>
      </IgnoreSpecificDefaultLibraries>
      <AdditionalLibraryDirectories>$(ProjectDir)..\3rd_party\DIA\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ModuleDefinitionFile>Exports.def</ModuleDefinitionFile>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>true</LinkLibraryDependencies>
    </ProjectReference>
    <PostBuildEvent>
      <Command>xcopy "$(ProjectDir)..\..\DIA\$(Platform)\msdia140.dll" "$(TargetDir)" /Y
xcopy "$(ProjectDir)..\..\DIA\$(Platform)\symsrv.dll" "$(TargetDir)" /Y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;BLACKBONE_NO_TRACE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <DisableSpecificWarnings>4100</DisableSpecificWarnings>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <AdditionalIncludeDirectories>$(ProjectDir)..;F:\Visual Studio2022\VC\Tools\MSVC\14.44.35207\atlmfc\include;$(VCToolsInstallDir)include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>true</LinkLibraryDependencies>
    </ProjectReference>
    <Lib>
      <AdditionalLibraryDirectories>$(ProjectDir)..\3rd_party\DIA\lib\amd64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>diaguids.lib;Ole32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
    <PostBuildEvent>
      <Command>xcopy "$(ProjectDir)..\..\DIA\$(Platform)\msdia140.dll" "$(TargetDir)" /Y
xcopy "$(ProjectDir)..\..\DIA\$(Platform)\symsrv.dll" "$(TargetDir)" /Y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release(XP)|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;BLACKBONE_NO_TRACE;XP_BUILD;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <DisableSpecificWarnings>4100</DisableSpecificWarnings>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <AdditionalIncludeDirectories>$(ProjectDir)..;F:\Visual Studio2022\VC\Tools\MSVC\14.44.35207\atlmfc\include;$(VCToolsInstallDir)include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>true</LinkLibraryDependencies>
    </ProjectReference>
    <Lib>
      <AdditionalLibraryDirectories>$(ProjectDir)..\3rd_party\DIA\lib\amd64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>diaguids.lib;Ole32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
    <PostBuildEvent>
      <Command>xcopy "$(ProjectDir)..\..\DIA\$(Platform)\msdia140.dll" "$(TargetDir)" /Y
xcopy "$(ProjectDir)..\..\DIA\$(Platform)\symsrv.dll" "$(TargetDir)" /Y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release(DLL)|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_DLL;BLACKBONE_NO_TRACE;BLACKBONE_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <DisableSpecificWarnings>4100</DisableSpecificWarnings>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <AdditionalIncludeDirectories>$(ProjectDir)..;F:\Visual Studio2022\VC\Tools\MSVC\14.44.35207\atlmfc\include;$(VCToolsInstallDir)include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>diaguids.lib;Ole32.lib;$(ProjectDir)..\3rd_party\BeaEngine\Win64\Dll\BeaEngineCheetah64.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(ProjectDir)..\3rd_party\DIA\lib\amd64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <ModuleDefinitionFile>Exports.def</ModuleDefinitionFile>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>true</LinkLibraryDependencies>
    </ProjectReference>
    <PostBuildEvent>
      <Command>xcopy "$(ProjectDir)..\..\DIA\$(Platform)\msdia140.dll" "$(TargetDir)" /Y
xcopy "$(ProjectDir)..\..\DIA\$(Platform)\symsrv.dll" "$(TargetDir)" /Y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\3rd_party\AsmJit\base\assembler.cpp" />
    <ClCompile Include="..\3rd_party\AsmJit\base\codegen.cpp" />
    <ClCompile Include="..\3rd_party\AsmJit\base\constpool.cpp" />
    <ClCompile Include="..\3rd_party\AsmJit\base\containers.cpp" />
    <ClCompile Include="..\3rd_party\AsmJit\base\cpuinfo.cpp" />
    <ClCompile Include="..\3rd_party\AsmJit\base\cputicks.cpp" />
    <ClCompile Include="..\3rd_party\AsmJit\base\error.cpp" />
    <ClCompile Include="..\3rd_party\AsmJit\base\globals.cpp" />
    <ClCompile Include="..\3rd_party\AsmJit\base\operand.cpp" />
    <ClCompile Include="..\3rd_party\AsmJit\base\runtime.cpp" />
    <ClCompile Include="..\3rd_party\AsmJit\base\string.cpp" />
    <ClCompile Include="..\3rd_party\AsmJit\base\vmem.cpp" />
    <ClCompile Include="..\3rd_party\AsmJit\base\zone.cpp" />
    <ClCompile Include="..\3rd_party\AsmJit\x86\x86assembler.cpp" />
    <ClCompile Include="..\3rd_party\AsmJit\x86\x86cpuinfo.cpp" />
    <ClCompile Include="..\3rd_party\AsmJit\x86\x86inst.cpp" />
    <ClCompile Include="..\3rd_party\AsmJit\x86\x86operand.cpp" />
    <ClCompile Include="..\3rd_party\AsmJit\x86\x86operand_regs.cpp" />
    <ClCompile Include="..\3rd_party\rewolf-wow64ext\src\wow64ext.cpp" />
    <ClCompile Include="Asm\AsmHelper32.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
      </ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug(XP)|x64'">
      </ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug(DLL)|x64'">
      </ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
      </ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release(XP)|x64'">
      </ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release(DLL)|x64'">
      </ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Asm\AsmHelper64.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
      </ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug(XP)|Win32'">
      </ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug(DLL)|Win32'">
      </ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release(XP)|Win32'">
      </ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release(DLL)|Win32'">
      </ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Asm\LDasm.c">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release(DLL)|Win32'">
      </ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="DriverControl\DriverControl.cpp" />
    <ClCompile Include="LocalHook\LocalHookBase.cpp" />
    <ClCompile Include="LocalHook\TraceHook.cpp" />
    <ClCompile Include="ManualMap\MExcept.cpp" />
    <ClCompile Include="ManualMap\MMap.cpp" />
    <ClCompile Include="ManualMap\Native\NtLoader.cpp" />
    <ClCompile Include="Misc\InitOnce.cpp" />
    <ClCompile Include="Misc\NameResolve.cpp" />
    <ClCompile Include="Misc\Utils.cpp" />
    <ClCompile Include="Patterns\PatternSearch.cpp" />
    <ClCompile Include="PE\ImageNET.cpp" />
    <ClCompile Include="PE\PEImage.cpp" />
    <ClCompile Include="Process\MemBlock.cpp" />
    <ClCompile Include="Process\Process.cpp" />
    <ClCompile Include="Process\ProcessCore.cpp" />
    <ClCompile Include="Process\ProcessMemory.cpp" />
    <ClCompile Include="Process\ProcessModules.cpp" />
    <ClCompile Include="Process\RPC\RemoteExec.cpp" />
    <ClCompile Include="Process\RPC\RemoteHook.cpp" />
    <ClCompile Include="Process\RPC\RemoteLocalHook.cpp" />
    <ClCompile Include="Process\RPC\RemoteMemory.cpp" />
    <ClCompile Include="Process\Threads\Thread.cpp" />
    <ClCompile Include="Process\Threads\Threads.cpp" />
    <ClCompile Include="DllMain.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug(XP)|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release(XP)|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug(XP)|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release(XP)|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="Subsystem\NativeSubsystem.cpp" />
    <ClCompile Include="Subsystem\Wow64Subsystem.cpp" />
    <ClCompile Include="Subsystem\x86Subsystem.cpp" />
    <ClCompile Include="Symbols\PatternLoader.cpp" />
    <ClCompile Include="Symbols\PDBHelper.cpp" />
    <ClCompile Include="Symbols\SymbolData.cpp" />
    <ClCompile Include="Symbols\SymbolLoader.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\3rd_party\AsmJit\apibegin.h" />
    <ClInclude Include="..\3rd_party\AsmJit\apiend.h" />
    <ClInclude Include="..\3rd_party\AsmJit\asmjit.h" />
    <ClInclude Include="..\3rd_party\AsmJit\base.h" />
    <ClInclude Include="..\3rd_party\AsmJit\base\assembler.h" />
    <ClInclude Include="..\3rd_party\AsmJit\base\codegen.h" />
    <ClInclude Include="..\3rd_party\AsmJit\base\constpool.h" />
    <ClInclude Include="..\3rd_party\AsmJit\base\containers.h" />
    <ClInclude Include="..\3rd_party\AsmJit\base\cpuinfo.h" />
    <ClInclude Include="..\3rd_party\AsmJit\base\cputicks.h" />
    <ClInclude Include="..\3rd_party\AsmJit\base\error.h" />
    <ClInclude Include="..\3rd_party\AsmJit\base\globals.h" />
    <ClInclude Include="..\3rd_party\AsmJit\base\lock.h" />
    <ClInclude Include="..\3rd_party\AsmJit\base\operand.h" />
    <ClInclude Include="..\3rd_party\AsmJit\base\runtime.h" />
    <ClInclude Include="..\3rd_party\AsmJit\base\string.h" />
    <ClInclude Include="..\3rd_party\AsmJit\base\vectypes.h" />
    <ClInclude Include="..\3rd_party\AsmJit\base\vmem.h" />
    <ClInclude Include="..\3rd_party\AsmJit\base\zone.h" />
    <ClInclude Include="..\3rd_party\AsmJit\build.h" />
    <ClInclude Include="..\3rd_party\AsmJit\config.h" />
    <ClInclude Include="..\3rd_party\AsmJit\host.h" />
    <ClInclude Include="..\3rd_party\AsmJit\x86.h" />
    <ClInclude Include="..\3rd_party\AsmJit\x86\x86assembler.h" />
    <ClInclude Include="..\3rd_party\AsmJit\x86\x86cpuinfo.h" />
    <ClInclude Include="..\3rd_party\AsmJit\x86\x86inst.h" />
    <ClInclude Include="..\3rd_party\AsmJit\x86\x86operand.h" />
    <ClInclude Include="..\3rd_party\rewolf-wow64ext\src\wow64ext.h" />
    <ClInclude Include="Asm\AsmFactory.h" />
    <ClInclude Include="Asm\AsmHelper32.h" />
    <ClInclude Include="Asm\AsmHelper64.h" />
    <ClInclude Include="Asm\IAsmHelper.h" />
    <ClInclude Include="Asm\AsmStack.hpp" />
    <ClInclude Include="Asm\AsmVariant.hpp" />
    <ClInclude Include="Asm\LDasm.h" />
    <ClInclude Include="Config.h" />
    <ClInclude Include="DriverControl\DriverControl.h" />
    <None Include="Exports.def" />
    <ClInclude Include="Include\ApiSet.h" />
    <ClInclude Include="Include\CallResult.h" />
    <ClInclude Include="Include\FunctionTypes.h" />
    <ClInclude Include="Include\HandleGuard.h" />
    <ClInclude Include="Include\Macro.h" />
    <ClInclude Include="Include\NativeEnums.h" />
    <ClInclude Include="Include\NativeStructures.h" />
    <ClInclude Include="Include\Types.h" />
    <ClInclude Include="Include\Win7Specific.h" />
    <ClInclude Include="Include\Win8Specific.h" />
    <ClInclude Include="Include\Winheaders.h" />
    <ClInclude Include="Include\WinXPSpecific.h" />
    <ClInclude Include="LocalHook\HookHandlerCdecl.h" />
    <ClInclude Include="LocalHook\HookHandlerFastcall.h" />
    <ClInclude Include="LocalHook\HookHandlers.h" />
    <ClInclude Include="LocalHook\HookHandlerStdcall.h" />
    <ClInclude Include="LocalHook\HookHandlerThiscall.h" />
    <ClInclude Include="LocalHook\LocalHook.hpp" />
    <ClInclude Include="LocalHook\LocalHookBase.h" />
    <ClInclude Include="LocalHook\TraceHook.h" />
    <ClInclude Include="LocalHook\VTableHook.hpp" />
    <ClInclude Include="ManualMap\MExcept.h" />
    <ClInclude Include="ManualMap\MMap.h" />
    <ClInclude Include="ManualMap\Native\NtLoader.h" />
    <ClInclude Include="Misc\DynImport.h" />
    <ClInclude Include="Misc\InitOnce.h" />
    <ClInclude Include="Misc\NameResolve.h" />
    <ClInclude Include="Misc\Thunk.hpp" />
    <ClInclude Include="Misc\Trace.hpp" />
    <ClInclude Include="Misc\Utils.h" />
    <ClInclude Include="Patterns\PatternSearch.h" />
    <ClInclude Include="PE\ImageNET.h" />
    <ClInclude Include="PE\PEImage.h" />
    <ClInclude Include="Process\MemBlock.h" />
    <ClInclude Include="Process\MultPtr.hpp" />
    <ClInclude Include="Process\Process.h" />
    <ClInclude Include="Process\ProcessCore.h" />
    <ClInclude Include="Process\ProcessMemory.h" />
    <ClInclude Include="Process\ProcessModules.h" />
    <ClInclude Include="Process\RPC\RemoteContext.hpp" />
    <ClInclude Include="Process\RPC\RemoteExec.h" />
    <ClInclude Include="Process\RPC\RemoteFunction.hpp" />
    <ClInclude Include="Process\RPC\RemoteHook.h" />
    <ClInclude Include="Process\RPC\RemoteLocalHook.h" />
    <ClInclude Include="Process\RPC\RemoteMemory.h" />
    <ClInclude Include="Process\Threads\Thread.h" />
    <ClInclude Include="Process\Threads\Threads.h" />
    <ClInclude Include="Subsystem\NativeSubsystem.h" />
    <ClInclude Include="Subsystem\Wow64Subsystem.h" />
    <ClInclude Include="Subsystem\x86Subsystem.h" />
    <ClInclude Include="Symbols\PatternLoader.h" />
    <ClInclude Include="Symbols\PDBHelper.h" />
    <ClInclude Include="Symbols\SymbolLoader.h" />
    <ClInclude Include="Symbols\SymbolData.h" />
    <ClInclude Include="Syscalls\Syscall.h" />
  </ItemGroup>
  <ItemGroup>
    <MASM Include="Syscalls\Syscall32.asm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug(XP)|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release(DLL)|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release(XP)|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug(DLL)|x64'">true</ExcludedFromBuild>
      <UseSafeExceptionHandlers Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</UseSafeExceptionHandlers>
      <UseSafeExceptionHandlers Condition="'$(Configuration)|$(Platform)'=='Release(DLL)|Win32'">true</UseSafeExceptionHandlers>
      <UseSafeExceptionHandlers Condition="'$(Configuration)|$(Platform)'=='Release(XP)|Win32'">true</UseSafeExceptionHandlers>
    </MASM>
    <MASM Include="Syscalls\Syscall64.asm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug(XP)|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release(DLL)|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release(XP)|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug(DLL)|Win32'">true</ExcludedFromBuild>
    </MASM>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.targets" />
  </ImportGroup>
</Project>